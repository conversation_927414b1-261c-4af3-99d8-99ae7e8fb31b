@echo off
setlocal enabledelayedexpansion

echo ========================================
echo    TS to MP4 Converter Script
echo ========================================
echo.

REM Check if FFmpeg is available
echo Checking for FFmpeg...
ffmpeg -version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: FFmpeg is not installed or not in PATH!
    echo.
    echo To install FFmpeg:
    echo 1. Download from: https://ffmpeg.org/download.html#build-windows
    echo 2. Extract to a folder (e.g., C:\ffmpeg)
    echo 3. Add C:\ffmpeg\bin to your system PATH environment variable
    echo 4. Restart command prompt and try again
    echo.
    echo Alternative: Use package managers like:
    echo - Chocolatey: choco install ffmpeg
    echo - Scoop: scoop install ffmpeg
    echo - Winget: winget install Gyan.FFmpeg
    echo.
    pause
    exit /b 1
)

echo FFmpeg found! Proceeding with conversion...
echo.

REM Count .ts files in current directory
set /a count=0
for %%f in (*.ts) do (
    set /a count+=1
)

if %count%==0 (
    echo No .ts files found in the current directory.
    echo Current directory: %cd%
    echo.
    pause
    exit /b 0
)

echo Found %count% .ts file(s) to convert.
echo.

REM Ask for confirmation
set /p confirm="Do you want to proceed with the conversion? (Y/N): "
if /i not "%confirm%"=="Y" if /i not "%confirm%"=="YES" (
    echo Conversion cancelled.
    pause
    exit /b 0
)

echo.
echo Starting conversion process...
echo ========================================

REM Initialize counters
set /a processed=0
set /a successful=0
set /a failed=0

REM Process each .ts file
for %%f in (*.ts) do (
    set /a processed+=1
    set "input_file=%%f"
    set "output_file=%%~nf.mp4"
    
    echo.
    echo [!processed!/!count!] Converting: !input_file! -^> !output_file!
    
    REM Check if output file already exists
    if exist "!output_file!" (
        echo WARNING: !output_file! already exists!
        set /p overwrite="Overwrite? (Y/N): "
        if /i not "!overwrite!"=="Y" if /i not "!overwrite!"=="YES" (
            echo Skipping !input_file!
            goto :continue
        )
    )
    
    REM Perform conversion with progress and error handling
    ffmpeg -i "!input_file!" -c:v libx264 -c:a aac -preset medium -crf 23 "!output_file!" -y
    
    if !errorlevel! equ 0 (
        echo SUCCESS: !input_file! converted successfully!
        set /a successful+=1
    ) else (
        echo ERROR: Failed to convert !input_file!
        set /a failed+=1
    )
    
    :continue
)

echo.
echo ========================================
echo Conversion Summary:
echo ========================================
echo Total files processed: %processed%
echo Successful conversions: %successful%
echo Failed conversions: %failed%
echo.

if %failed% gtr 0 (
    echo Some conversions failed. Check the error messages above.
) else (
    echo All conversions completed successfully!
)

echo.
pause
